# 项目架构指南

本文档定义了项目的标准目录结构、代码组织方式和核心设计原则。所有开发者都应遵循此指南，以确保代码的一致性、可维护性和可扩展性。

## 1. 根目录结构 (Monorepo)

项目采用 Monorepo 结构，将所有相关的子项目（如前端、后端、共享库）都放在一个版本库中。

```
/cheestack
├── .vscode/                # VS Code 编辑器配置
│   ├── settings.json
│   ├── launch.json         # 调试配置
│   └── extensions.json     # 推荐扩展
├── cheestack-fastapi/      # FastAPI 后端服务
│   ├── apps/               # 应用模块
│   │   ├── auth/           # 认证模块
│   │   ├── study/          # 学习模块
│   │   └── general/        # 通用模块
│   ├── core/               # 核心配置
│   │   ├── config.py       # 配置管理
│   │   ├── postgresql.py   # 数据库配置
│   │   ├── redis.py        # Redis 配置
│   │   └── routers.py      # 路由管理
│   ├── sutils/             # 共享工具
│   ├── tests/              # 测试代码
│   ├── migrations/         # 数据库迁移
│   ├── docker/             # Docker 配置
│   │   ├── Dockerfile
│   │   ├── docker-compose.yml
│   │   └── nginx.conf
│   ├── k3s/                # K3s 部署配置
│   │   ├── namespace.yaml
│   │   ├── configmap.yaml
│   │   ├── secret.yaml
│   │   ├── postgres.yaml
│   │   ├── redis.yaml
│   │   └── backend.yaml
│   ├── scripts/            # 部署脚本
│   │   └── deploy.sh
│   ├── main.py             # 应用入口
│   ├── pyproject.toml      # Python 项目配置
│   └── requirements.txt    # 依赖配置
├── cheestack-flt/          # Flutter 前端应用
│   ├── lib/
│   │   ├── core/           # 核心功能
│   │   ├── features/       # 功能模块
│   │   ├── shared/         # 共享组件
│   │   ├── controllers/    # GetX 控制器
│   │   ├── services/       # 服务层
│   │   ├── models/         # 数据模型
│   │   ├── pages/          # 页面
│   │   ├── widgets/        # 组件
│   │   ├── routes/         # 路由配置
│   │   └── main.dart       # 应用入口
│   ├── test/               # 测试代码
│   ├── scripts/            # 构建脚本
│   │   └── build.sh
│   ├── assets/             # 资源文件
│   ├── analysis_options.yaml
│   └── pubspec.yaml
├── docs/                   # 项目文档
│   ├── ARCHITECTURE.md
│   └── README.md
│
├── .env.example            # 环境变量模板
├── .gitignore
├── .pre-commit-config.yaml # 代码提交钩子
├── .editorconfig          # 编辑器配置
├── pyproject.toml         # Python 工具配置
└── README.md
```

### 目录说明

*   **`.vscode/`**: 存放项目级的 VS Code 配置，包括调试配置、工作区设置和推荐扩展，确保团队成员拥有一致的开发环境。
*   **`cheestack-fastapi/`**: FastAPI 后端服务，包含完整的 API 服务、数据库配置、部署脚本等。
*   **`cheestack-flt/`**: Flutter 前端应用，使用 GetX 进行状态管理的跨平台移动应用。
*   **`docs/`**: 项目文档，包括架构说明、API 文档等。

## 2. 后端架构 (FastAPI)

后端服务采用分层架构，使用 Tortoise ORM 作为数据访问层，将不同的职责分离到独立的模块中，并集成完整的错误处理、安全和监控机制。


你是一位 Python、FastAPI 和可扩展 API 开发领域的专家，专门针对 CheeStack 项目架构进行开发。

### 关键原则
- 使用函数式和声明式编程，尽量避免使用类（除了 Pydantic 模型和 Tortoise 模型）
- 更倾向于迭代和模块化，而不是代码重复
- 路由和工具函数优先使用命名导出
- 遵循“接收对象，返回对象”（RORO）模式

### 项目架构规范
#### 完整目录结构
```
project/
├── apps/                        # 应用模块目录
│   ├── app_name1/                 # app名称
│   │   ├── __init__.py           # 模块初始化
│   │   ├── apis.py               # API 路由定义
│   │   ├── models.py             # Tortoise ORM 数据模型
│   │   ├── schema.py             # Pydantic 数据验证模型
│   │   ├── deps.py               # 依赖注入函数
│   │   ├── curd.py               # 数据库 CRUD 操作
│   │   ├── config.py             # 模块配置
│   │   ├── consts.py             # 模块常量
│   │   └── views.py              # 视图函数（可选）
│   │   └── README.md              # app 说明文档
│   ├── app_name2/                 # app名称
│   │   └── ...              # 文件架构参考app_name1
├── core/                          # 核心功能目录
│   ├── __init__.py               # 核心模块初始化
│   ├── config.py                 # 应用配置
│   ├── constants.py              # 全局常量
│   ├── events.py                 # 应用事件处理
│   ├── exceptions.py             # 异常处理器
│   ├── fields.py                 # 自定义字段类型
│   ├── schema.py                 # 基础响应模式
│   ├── http.py                   # HTTP 相关工具
│   ├── middleware.py             # 中间件定义
│   ├── models.py                 # 基础数据模型
│   ├── postgresql.py             # PostgreSQL 数据库配置
│   ├── redis.py                  # Redis 缓存配置
│   ├── responses.py              # 响应处理器
│   ├── routers.py                # 路由配置
│   └── setting.py                # 设置管理
├── static/                        # 静态文件目录
│   └── templates/                # 模板文件
├── assets/                        # 资源文件
│   ├── sources.list              # 系统源配置
│   └── *.mp3                     # 音频文件
├── tests/                         # 测试文件目录
│   ├── __init__.py
│   └── test.py                   # 测试用例
├── main.py                        # 应用入口文件
├── requirements.in                # 依赖需求文件
├── requirements.txt               # 锁定的依赖版本
├── pyproject.toml                 # 项目配置文件
├── dockerfile                     # Docker 配置
├── git-push-version.sh            # 版本推送脚本
└── data_resorted.py              # 数据整理脚本
```


#### 开发规范和最佳实践

##### 模块化设计原则
- **单一职责原则**: 每个模块只负责一个业务领域
- **高内聚低耦合**: 模块内部功能紧密相关，模块间依赖最小化
- **分层架构**: API层、业务逻辑层、数据访问层分离
- **依赖注入**: 使用 FastAPI 的依赖注入系统管理组件

##### 技术栈规范
- **Web框架**: FastAPI (异步高性能)
- **ORM**: Tortoise ORM (异步ORM，类似Django ORM)
- **数据库**: PostgreSQL (主数据库) + Redis (缓存)
- **数据验证**: Pydantic (数据模型和验证)
- **数据库迁移**: Aerich (Tortoise ORM的迁移工具)
- **异步HTTP客户端**: httpx
- **密码加密**: passlib
- **JWT认证**: PyJWT
- **配置管理**: pydantic-settings
- **测试框架**: pytest + pytest-asyncio

##### 文件命名和结构规范

###### 应用模块文件结构
每个应用模块必须包含以下文件：
- `__init__.py`: 模块初始化文件
- `apis.py`: API路由定义，使用APIRouter
- `models.py`: Tortoise ORM数据模型定义
- `schema.py`: Pydantic数据验证和序列化模型
- `deps.py`: 依赖注入函数定义
- `curd.py` 或 `crud.py`: 数据库CRUD操作函数
- `config.py`: 模块特定配置（可选）
- `consts.py`: 模块常量定义（可选）
- `views.py`: 视图函数（可选，用于模板渲染）

###### 核心模块文件说明
- `config.py`: 应用全局配置，使用pydantic-settings
- `constants.py`: 全局常量定义
- `events.py`: FastAPI应用事件处理（startup/shutdown）
- `exceptions.py`: 全局异常处理器
- `fields.py`: 自定义Tortoise ORM字段类型
- `schema.py`: 基础响应模式和通用Pydantic模型
- `http.py`: HTTP状态码和相关工具
- `middleware.py`: 中间件定义和配置
- `models.py`: 基础数据模型和Mixin类
- `postgresql.py`: PostgreSQL数据库连接配置
- `redis.py`: Redis缓存连接配置
- `responses.py`: 自定义响应处理器
- `routers.py`: 路由聚合和配置
- `setting.py`: 设置管理工具


##### 响应格式规范

###### 统一响应格式
```python
# 基础响应模型
class BaseResponseModel(BaseModel):
    code: int = HttpCode.SUCCESS
    msg: str = "success"
    data: Any = []

# 使用resmod函数创建特定响应模型
@router.get("/users", response_model=resmod(list[UserSchemaOut]))
async def get_users():
    users = await User.all()
    return ApiResponse.success(data=users)
```

###### 错误处理规范
```python
# 自定义异常
class Sexception(HTTPException):
    def __init__(self, code: int, msg: str):
        super().__init__(status_code=code, detail=msg)

# 全局异常处理器
@app.exception_handler(Sexception)
async def custom_exception_handler(request: Request, exc: Sexception):
    return JSONResponse(
        status_code=exc.status_code,
        content={"code": exc.status_code, "msg": exc.detail, "data": []}
    )
```

##### 数据库操作规范

###### 事务处理
```python
from tortoise.transactions import in_transaction

async def transfer_money(from_user_id: str, to_user_id: str, amount: float):
    async with in_transaction() as connection:
        # 在事务中执行多个数据库操作
        from_user = await User.get(id=from_user_id).using_db(connection)
        to_user = await User.get(id=to_user_id).using_db(connection)

        from_user.balance -= amount
        to_user.balance += amount

        await from_user.save(using_db=connection)
        await to_user.save(using_db=connection)
```

###### 查询优化
```python
# 使用select_related和prefetch_related优化查询
users = await User.all().select_related("role").prefetch_related("books")

# 使用Q对象进行复杂查询
from tortoise.expressions import Q
users = await User.filter(
    Q(username__icontains="admin") | Q(email__icontains="admin")
)
```

##### 安全规范

###### 密码处理
```python
from passlib.context import CryptContext

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def hash_password(password: str) -> str:
    """加密密码"""
    return pwd_context.hash(password)

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """验证密码"""
    return pwd_context.verify(plain_password, hashed_password)
```

###### JWT认证
```python
import jwt
from datetime import datetime, timedelta

def create_access_token(data: dict, expires_delta: timedelta = None):
    """创建访问令牌"""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)

    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt
```

##### 测试规范

###### 单元测试
```python
import pytest
from httpx import AsyncClient
from main import app

@pytest.mark.asyncio
async def test_create_user():
    async with AsyncClient(app=app, base_url="http://test") as ac:
        response = await ac.post(
            "/v1/auth/register",
            json={"username": "testuser", "password": "testpass123"}
        )
    assert response.status_code == 200
    assert response.json()["code"] == 200
```

###### 数据库测试
```python
@pytest.fixture
async def setup_database():
    # 设置测试数据库
    await Tortoise.init(
        db_url="sqlite://:memory:",
        modules={"models": ["apps.auth.models"]}
    )
    await Tortoise.generate_schemas()
    yield
    await Tortoise.close_connections()

@pytest.mark.asyncio
async def test_user_crud(setup_database):
    # 测试用户CRUD操作
    user = await User.create(username="test", password="hashed_password")
    assert user.username == "test"

    retrieved_user = await User.get(id=user.id)
    assert retrieved_user.username == "test"
```

##### 性能优化规范

###### 异步操作
```python
# 使用异步函数处理I/O操作
async def fetch_external_data():
    async with httpx.AsyncClient() as client:
        response = await client.get("https://api.example.com/data")
        return response.json()

# 并发处理多个异步任务
import asyncio

async def process_multiple_users(user_ids: list[str]):
    tasks = [process_user(user_id) for user_id in user_ids]
    results = await asyncio.gather(*tasks)
    return results
```

###### 缓存策略
```python
import redis
from functools import wraps

redis_client = redis.Redis.from_url(settings.REDIS_URL)

def cache_result(expire_time: int = 300):
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            cache_key = f"{func.__name__}:{hash(str(args) + str(kwargs))}"

            # 尝试从缓存获取
            cached_result = redis_client.get(cache_key)
            if cached_result:
                return json.loads(cached_result)

            # 执行函数并缓存结果
            result = await func(*args, **kwargs)
            redis_client.setex(
                cache_key,
                expire_time,
                json.dumps(result, default=str)
            )
            return result
        return wrapper
    return decorator
```

##### 日志和监控规范

###### 日志配置
```python
import logging
from sutils.log import Slog

# 使用结构化日志
logger = Slog.get_logger(__name__)

@router.post("/login")
async def login(data: AuthModel):
    logger.info("用户登录尝试", extra={
        "username": data.username,
        "ip": request.client.host,
        "user_agent": request.headers.get("user-agent")
    })

    try:
        user = await authenticate_user(data.username, data.password)
        logger.info("用户登录成功", extra={"user_id": user.id})
        return user
    except Exception as e:
        logger.error("用户登录失败", extra={
            "username": data.username,
            "error": str(e)
        })
        raise
```

###### 健康检查
```python
@router.get("/health")
async def health_check():
    """健康检查接口"""
    try:
        # 检查数据库连接
        await User.first()

        # 检查Redis连接
        redis_client.ping()

        return {"status": "healthy", "timestamp": datetime.utcnow()}
    except Exception as e:
        raise HTTPException(
            status_code=503,
            detail=f"Service unhealthy: {str(e)}"
        )
```

##### 部署和配置规范

###### 环境配置
```python
# 使用pydantic-settings管理配置
class Settings(BaseSettings):
    # 数据库配置
    DATABASE_URL: str
    REDIS_URL: str

    # 安全配置
    SECRET_KEY: str
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30

    # 应用配置
    DEBUG: bool = False
    CORS_ORIGINS: list[str] = []

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"

settings = Settings()
```

###### Docker配置
```dockerfile
FROM python:3.12-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY . .

EXPOSE 8000

CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### 代码质量检查清单

#### 提交前检查
- [ ] 所有异步函数都使用了`async/await`
- [ ] 数据库操作都进行了适当的错误处理
- [ ] API接口都有完整的类型注解和文档字符串
- [ ] 敏感信息（密码、令牌）都进行了适当的加密和保护
- [ ] 所有路由都有适当的权限检查
- [ ] 代码遵循PEP 8规范
- [ ] 单元测试覆盖率达到要求
- [ ] 没有硬编码的配置信息

#### 性能检查
- [ ] 数据库查询进行了优化（避免N+1问题）
- [ ] 适当使用了缓存机制
- [ ] 大量数据处理使用了分页
- [ ] 文件上传有大小限制
- [ ] API响应时间在可接受范围内

#### 安全检查
- [ ] 输入数据都进行了验证和清理
- [ ] SQL注入防护到位
- [ ] XSS攻击防护到位
- [ ] CSRF攻击防护到位
- [ ] 敏感操作都有权限验证
- [ ] 错误信息不泄露敏感信息

### 常见问题和解决方案

#### 数据库相关
**问题**: Tortoise ORM查询性能问题
**解决方案**:
```python
# 使用select_related避免N+1查询
users = await User.all().select_related("role")

# 使用prefetch_related处理多对多关系
users = await User.all().prefetch_related("books")

# 使用values()只获取需要的字段
user_names = await User.all().values("id", "username")
```

**问题**: 数据库连接池配置
**解决方案**:
```python
# 在core/postgresql.py中配置连接池
TORTOISE_ORM = {
    "connections": {
        "default": {
            "engine": "tortoise.backends.asyncpg",
            "credentials": {
                "host": "localhost",
                "port": "5432",
                "user": "postgres",
                "password": "password",
                "database": "cheestack",
                "minsize": 1,
                "maxsize": 20,
            }
        }
    }
}
```

#### 异步编程相关
**问题**: 混用同步和异步代码
**解决方案**:
```python
# 错误示例
def sync_function():
    return User.all()  # 这会导致错误

# 正确示例
async def async_function():
    return await User.all()

# 如果必须在异步函数中调用同步代码
import asyncio
from concurrent.futures import ThreadPoolExecutor

async def call_sync_in_async():
    loop = asyncio.get_event_loop()
    with ThreadPoolExecutor() as executor:
        result = await loop.run_in_executor(executor, sync_function)
    return result
```

#### 依赖注入相关
**问题**: 依赖注入函数的复用和组合
**解决方案**:
```python
# 基础依赖
async def get_db():
    # 返回数据库连接
    pass

# 组合依赖
async def get_current_user(db = Depends(get_db)):
    # 使用数据库连接获取用户
    pass

async def get_admin_user(current_user: User = Depends(get_current_user)):
    if not current_user.is_admin:
        raise HTTPException(status_code=403, detail="Admin required")
    return current_user
```

#### 文件上传处理
**问题**: 大文件上传和存储
**解决方案**:
```python
from fastapi import File, UploadFile
import aiofiles
import os

@router.post("/upload")
async def upload_file(file: UploadFile = File(...)):
    # 检查文件大小
    if file.size > 10 * 1024 * 1024:  # 10MB
        raise HTTPException(status_code=413, detail="File too large")

    # 检查文件类型
    allowed_types = ["image/jpeg", "image/png", "application/pdf"]
    if file.content_type not in allowed_types:
        raise HTTPException(status_code=400, detail="File type not allowed")

    # 异步保存文件
    file_path = f"uploads/{file.filename}"
    async with aiofiles.open(file_path, 'wb') as f:
        content = await file.read()
        await f.write(content)

    return {"filename": file.filename, "path": file_path}
```

### 项目特定规范

#### CheeStack项目特殊要求
- **语音处理**: 使用azure-cognitiveservices-speech和edge-tts处理语音相关功能
- **云服务集成**: 支持阿里云和腾讯云服务集成
- **多媒体处理**: 支持音频文件处理和TTS功能
- **学习应用**: 针对教育场景的特殊业务逻辑

#### 工具函数使用规范
```python
# 使用sutils工具包中的实用函数
from sutils.log import Slog
from sutils.string import Sstring
from sutils.datetime import format_datetime
from sutils.validator import Spattern

# 日志记录
logger = Slog.get_logger(__name__)

# 字符串处理
cleaned_text = Sstring.clean_text(user_input)

# 数据验证
if not Spattern.mobile.match(phone_number):
    raise ValueError("Invalid phone number")
```

### 开发工作流程

#### 新功能开发流程
1. **需求分析**: 明确功能需求和API设计
2. **数据模型设计**: 设计Tortoise ORM模型
3. **API接口设计**: 定义路由和Pydantic模型
4. **依赖注入设计**: 设计必要的依赖函数
5. **业务逻辑实现**: 实现CRUD和业务逻辑
6. **单元测试编写**: 编写完整的测试用例
7. **集成测试**: 测试API接口和数据库交互
8. **文档更新**: 更新API文档和代码注释

#### 代码审查要点
- **架构一致性**: 是否遵循项目架构规范
- **代码质量**: 是否符合代码风格规范
- **性能考虑**: 是否有性能优化空间
- **安全性**: 是否存在安全漏洞
- **测试覆盖**: 测试用例是否充分
- **文档完整性**: 代码注释和API文档是否完整

### 维护和监控

#### 日常维护任务
- **数据库维护**: 定期检查数据库性能和索引优化
- **日志监控**: 监控应用日志，及时发现问题
- **性能监控**: 监控API响应时间和系统资源使用
- **安全更新**: 定期更新依赖包，修复安全漏洞
- **备份策略**: 定期备份数据库和重要文件

#### 故障排查指南
1. **检查日志**: 查看应用日志和错误信息
2. **数据库连接**: 检查数据库连接状态
3. **Redis连接**: 检查缓存服务状态
4. **依赖服务**: 检查外部服务可用性
5. **资源使用**: 检查CPU、内存、磁盘使用情况
6. **网络连接**: 检查网络连接和防火墙设置

### 总结

本规范文档涵盖了CheeStack FastAPI项目的完整开发指南，包括：
- 项目架构和文件组织
- 代码风格和最佳实践
- 数据库操作和ORM使用
- API设计和文档规范
- 安全性和性能优化
- 测试和部署流程
- 维护和监控策略

遵循这些规范可以确保代码质量、提高开发效率、降低维护成本，并保持项目的长期可维护性。


### 2.1. 目录结构

```
cheestack-fastapi/
├── apps/                   # 应用模块
│   ├── __init__.py
│   ├── auth/               # 认证模块
│   │   ├── __init__.py
│   │   ├── models.py       # 数据模型
│   │   ├── schemas.py      # Pydantic 模式
│   │   ├── views.py        # API 视图
│   │   └── utils.py        # 工具函数
│   ├── study/              # 学习模块
│   │   ├── __init__.py
│   │   ├── models.py
│   │   ├── schemas.py
│   │   ├── views.py
│   │   └── utils.py
│   └── general/            # 通用模块
│       ├── __init__.py
│       ├── models.py
│       ├── schemas.py
│       ├── views.py
│       └── utils.py
├── core/                   # 核心配置
│   ├── __init__.py
│   ├── config.py           # 应用配置
│   ├── postgresql.py       # PostgreSQL 配置
│   ├── redis.py            # Redis 配置
│   ├── routers.py          # 路由管理
│   ├── security.py         # 安全工具
│   ├── middleware.py       # 中间件
│   ├── exceptions.py       # 异常处理
│   └── responses.py        # 响应格式
├── sutils/                 # 共享工具
│   ├── __init__.py
│   ├── database.py         # 数据库工具
│   ├── auth.py             # 认证工具
│   ├── validators.py       # 验证器
│   └── helpers.py          # 辅助函数
├── tests/                  # 测试代码
│   ├── __init__.py
│   ├── test_auth.py        # 认证测试
│   ├── test_study.py       # 学习测试
│   └── conftest.py         # pytest 配置
├── migrations/             # 数据库迁移
├── docker/                 # Docker 配置
│   ├── Dockerfile
│   ├── docker-compose.yml
│   └── nginx.conf
├── k3s/                    # K3s 部署配置
│   ├── namespace.yaml
│   ├── configmap.yaml
│   ├── secret.yaml
│   ├── postgres.yaml
│   ├── redis.yaml
│   └── backend.yaml
├── scripts/                # 部署脚本
│   └── deploy.sh
├── main.py                 # FastAPI 应用入口
├── pyproject.toml          # 项目配置
├── requirements.txt        # 依赖配置
└── README.md
```

### 2.2. 统一依赖注入架构

#### 依赖注入设计原则
*   **单一职责**: 每个依赖只负责一个特定功能
*   **接口隔离**: 通过抽象接口定义依赖契约
*   **依赖反转**: 高层模块不依赖低层模块
*   **生命周期管理**: 单例、请求级、瞬态等不同生命周期

#### 统一依赖管理示例
```python
# app/core/dependencies.py
from functools import lru_cache
from typing import AsyncGenerator, Annotated
from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from tortoise.contrib.fastapi import get_connection

from app.core.database import DatabaseManager
from app.core.settings import get_settings
from app.core.security import JWTManager, PasswordManager
from app.features.auth.repository import AuthRepository
from app.features.auth.service import AuthService

# ============= 应用级依赖 (单例) =============

@lru_cache()
def get_settings():
    """获取应用配置 - 单例"""
    return Settings()

@lru_cache()
def get_jwt_manager() -> JWTManager:
    """获取JWT管理器 - 单例"""
    settings = get_settings()
    return JWTManager(
        secret_key=settings.SECRET_KEY,
        algorithm=settings.ALGORITHM,
        access_token_expire_minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES,
    )

@lru_cache()
def get_password_manager() -> PasswordManager:
    """获取密码管理器 - 单例"""
    return PasswordManager()

@lru_cache()
def get_database_manager() -> DatabaseManager:
    """获取数据库管理器 - 单例"""
    return DatabaseManager()

# ============= 请求级依赖 =============

async def get_database_connection():
    """获取数据库连接 - 请求级"""
    connection = get_connection("default")
    try:
        yield connection
    finally:
        # 连接会被 Tortoise 自动管理
        pass

async def get_auth_repository(
    db_manager: Annotated[DatabaseManager, Depends(get_database_manager)]
) -> AuthRepository:
    """获取认证仓储 - 请求级"""
    return AuthRepository(db_manager)

async def get_auth_service(
    auth_repo: Annotated[AuthRepository, Depends(get_auth_repository)],
    password_manager: Annotated[PasswordManager, Depends(get_password_manager)],
    jwt_manager: Annotated[JWTManager, Depends(get_jwt_manager)]
) -> AuthService:
    """获取认证服务 - 请求级"""
    return AuthService(
        auth_repository=auth_repo,
        password_manager=password_manager,
        jwt_manager=jwt_manager
    )

# ============= 认证和权限依赖 =============

security = HTTPBearer()

async def get_current_user_id(
    credentials: Annotated[HTTPAuthorizationCredentials, Depends(security)],
    jwt_manager: Annotated[JWTManager, Depends(get_jwt_manager)]
) -> int:
    """获取当前用户ID"""
    try:
        payload = jwt_manager.verify_token(credentials.credentials)
        user_id = payload.get("sub")
        if user_id is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的认证凭据",
                headers={"WWW-Authenticate": "Bearer"},
            )
        return int(user_id)
    except Exception:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的认证凭据",
            headers={"WWW-Authenticate": "Bearer"},
        )

async def get_current_user(
    user_id: Annotated[int, Depends(get_current_user_id)],
    auth_service: Annotated[AuthService, Depends(get_auth_service)]
):
    """获取当前用户完整信息"""
    user = await auth_service.get_user_by_id(user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户不存在"
        )
    return user

async def get_admin_user(
    current_user = Depends(get_current_user)
):
    """获取管理员用户 - 权限检查"""
    if not current_user.is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要管理员权限"
        )
    return current_user

# ============= 工具类依赖 =============

class DependencyProvider:
    """依赖提供者 - 统一管理复杂依赖"""
    
    _instances = {}
    
    @classmethod
    def get_service(cls, service_type: type):
        """获取服务实例"""
        if service_type not in cls._instances:
            cls._instances[service_type] = cls._create_service(service_type)
        return cls._instances[service_type]
    
    @classmethod
    def _create_service(cls, service_type: type):
        """创建服务实例 - 可以根据类型动态创建"""
        # 这里可以实现更复杂的依赖创建逻辑
        if service_type == AuthService:
            return AuthService(
                auth_repository=cls.get_service(AuthRepository),
                password_manager=get_password_manager(),
                jwt_manager=get_jwt_manager()
            )
        # 添加其他服务类型...
        raise ValueError(f"未知的服务类型: {service_type}")

# ============= 验证和中间件依赖 =============

def validate_pagination(
    page: int = 1,
    size: int = 20,
    max_size: int = 100
):
    """分页参数验证"""
    if page < 1:
        raise HTTPException(status_code=400, detail="页码必须大于0")
    if size < 1 or size > max_size:
        raise HTTPException(status_code=400, detail=f"每页大小必须在1-{max_size}之间")
    return {"page": page, "size": size, "offset": (page - 1) * size}

async def rate_limit_check(request):
    """限流检查 - 可以集成 Redis 等"""
    # 实现限流逻辑
    pass
```

#### 在路由中使用依赖注入
```python
# app/features/auth/router.py
from fastapi import APIRouter, Depends
from app.core.dependencies import get_auth_service, get_current_user

router = APIRouter()

@router.post("/login")
async def login(
    login_data: LoginRequest,
    auth_service: AuthService = Depends(get_auth_service)
):
    return await auth_service.login(login_data.email, login_data.password)

@router.get("/profile")
async def get_profile(
    current_user = Depends(get_current_user)
):
    return current_user
```

### 2.2. 分层架构详解

*   **Presentation Layer (`router.py`)**: 负责处理 HTTP 请求和响应，定义 API 路径、参数验证和响应模型，调用 `Service` 层执行业务逻辑，不包含任何业务逻辑。
*   **Application/Service Layer (`service.py`)**: 包含核心的业务逻辑和用例实现，协调多个 `Repository` 或外部服务，处理数据转换和业务规则验证，以及事务管理和业务异常处理。
*   **Data Access Layer (`repository.py`)**: 封装数据访问逻辑，使用 Tortoise ORM 进行数据库操作，负责查询优化和数据库连接管理。
*   **Data Models (`models.py` & `schema.py`)**: `models.py` 定义数据库表结构 (ORM 模型)，`schema.py` 定义 API 数据传输对象 (Pydantic 模型)。

#### Presentation Layer (`router.py`)

#### 安全机制
*   JWT 认证和刷新令牌机制
*   基于角色的访问控制 (RBAC)
*   API 限流和防止暴力破解
*   数据加密和敏感信息保护

#### 中间件栈
*   CORS 配置
*   请求日志记录
*   异常捕获和统一处理
*   性能监控和指标收集
*   请求追踪和链路监控

### 2.4. 安全和中间件配置

1. **请求**: 客户端向 `/auth/login` 发送 POST 请求
2. **中间件**: 请求经过中间件栈处理 (日志、限流等)
3. **路由 (`router.py`)**: `auth_router` 接收并验证请求数据
4. **服务 (`service.py`)**: 调用 `AuthService.login()` 执行业务逻辑
5. **数据访问**: 通过 Tortoise ORM 模型查询用户信息
6. **安全验证**: 验证密码并生成 JWT 令牌
7. **响应**: 返回标准化的 API 响应

### 2.5. 数据流示例 (用户登录)

前端应用遵循 **Clean Architecture** 和 **DDD (Domain-Driven Design)** 原则，使用 **GetX** 作为状态管理解决方案，结合现代组件化开发。


## 3. 前端架构 (Flutter + GetX)
### 3.1. 目录结构

```
cheestack-flt/
├── lib/
│   ├── core/               # 核心功能
│   │   ├── config/         # 配置管理
│   │   │   ├── app_config.dart
│   │   │   └── env_config.dart
│   │   ├── network/        # 网络配置
│   │   │   ├── dio_client.dart
│   │   │   ├── interceptors/
│   │   │   │   ├── auth_interceptor.dart
│   │   │   │   ├── error_interceptor.dart
│   │   │   │   └── logging_interceptor.dart
│   │   │   └── network_info.dart
│   │   ├── storage/        # 本地存储
│   │   │   ├── storage_service.dart
│   │   │   └── secure_storage.dart
│   │   ├── services/       # 核心服务
│   │   │   ├── app_service.dart
│   │   │   ├── auth_service.dart
│   │   │   └── api_service.dart
│   │   ├── theme/          # 主题系统
│   │   │   ├── app_theme.dart
│   │   │   ├── colors.dart
│   │   │   └── text_styles.dart
│   │   └── utils/          # 工具函数
│   │       ├── validators.dart
│   │       ├── formatters.dart
│   │       ├── extensions.dart
│   │       └── device_utils.dart
│   ├── shared/             # 共享组件
│   │   ├── theme/          # 主题配置
│   │   │   ├── app_theme.dart
│   │   │   └── theme_controller.dart
│   │   ├── routes/         # 路由配置
│   │   │   ├── app_pages.dart
│   │   │   ├── app_routes.dart
│   │   │   └── middlewares.dart
│   │   ├── translations/   # 国际化
│   │   │   ├── app_translations.dart
│   │   │   └── translation_controller.dart
│   │   ├── bindings/       # GetX 绑定
│   │   │   ├── initial_binding.dart
│   │   │   └── auth_binding.dart
│   │   ├── widgets/        # 通用组件
│   │   │   ├── buttons/
│   │   │   ├── forms/
│   │   │   ├── loading/
│   │   │   └── dialogs/
│   │   └── utils/          # 共享工具
│   │       ├── ui_utils.dart
│   │       ├── date_utils.dart
│   │       └── device_utils.dart
│   ├── features/           # 功能模块
│   │   ├── splash/         # 启动页
│   │   │   ├── splash_controller.dart
│   │   │   ├── splash_page.dart
│   │   │   └── splash_binding.dart
│   │   ├── auth/           # 认证模块
│   │   │   ├── controllers/
│   │   │   │   ├── auth_controller.dart
│   │   │   │   └── login_controller.dart
│   │   │   ├── models/
│   │   │   │   ├── user_model.dart
│   │   │   │   └── auth_request.dart
│   │   │   ├── pages/
│   │   │   │   ├── login_page.dart
│   │   │   │   └── register_page.dart
│   │   │   ├── widgets/
│   │   │   │   ├── login_form.dart
│   │   │   │   └── auth_button.dart
│   │   │   ├── services/
│   │   │   │   └── auth_service.dart
│   │   │   └── bindings/
│   │   │       └── auth_binding.dart
│   │   └── home/           # 首页模块
│   │       ├── controllers/
│   │       │   └── home_controller.dart
│   │       ├── pages/
│   │       │   └── home_page.dart
│   │       ├── widgets/
│   │       │   └── home_widgets.dart
│   │       └── bindings/
│   │           └── home_binding.dart
│   └── main.dart           # 应用入口
├── test/                     # 测试代码
│   ├── unit/                 # 单元测试
│   │   ├── controllers/      # 控制器测试
│   │   │   ├── auth_controller_test.dart
│   │   │   └── home_controller_test.dart
│   │   ├── services/         # 服务测试
│   │   │   ├── auth_service_test.dart
│   │   │   └── api_service_test.dart
│   │   └── models/           # 模型测试
│   │       └── user_model_test.dart
│   ├── widget/               # Widget 测试
│   │   ├── auth/
│   │   │   ├── login_page_test.dart
│   │   │   └── login_form_test.dart
│   │   └── home/
│   │       └── home_page_test.dart
│   ├── integration/          # 集成测试
│   │   ├── auth_flow_test.dart
│   │   └── app_flow_test.dart
│   ├── mocks/                # Mock 对象
│   │   ├── mock_auth_service.dart
│   │   ├── mock_api_service.dart
│   │   └── mock_storage_service.dart
│   └── test_utils.dart       # 测试工具
├── assets/                   # 资源文件
│   ├── images/
│   ├── icons/
│   └── fonts/
├── analysis_options.yaml     # Dart 分析选项
├── pubspec.yaml              # 项目配置
└── README.md
```

### 3.2. GetX 状态管理架构

#### GetX 核心概念
*   **Controller**: 业务逻辑和状态管理
*   **Binding**: 依赖注入和生命周期管理
*   **GetView**: 响应式 UI 组件
*   **Reactive Variables**: 响应式状态变量

#### GetX 状态分层
*   **GetxController**: 核心控制器，管理复杂业务逻辑
*   **GetxService**: 全局服务，跨页面状态共享
*   **Local State**: 简单的页面内状态使用 Rx 变量

#### 依赖注入策略
```dart
// bindings.dart
class AuthBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<AuthController>(
      () => AuthController(
        loginUseCase: Get.find(),
        logoutUseCase: Get.find(),
      ),
    );
    
    Get.lazyPut<AuthRepository>(
      () => AuthRepositoryImpl(
        remoteDataSource: Get.find(),
        localDataSource: Get.find(),
      ),
    );
  }
}
```

#### 状态响应式更新
```dart
// auth_controller.dart
class AuthController extends GetxController {
  final _isLoading = false.obs;
  final _user = Rxn<User>();
  final _authStatus = AuthStatus.initial.obs;
  
  bool get isLoading => _isLoading.value;
  User? get user => _user.value;
  AuthStatus get authStatus => _authStatus.value;
  
  Future<void> login(String email, String password) async {
    _isLoading.value = true;
    try {
      final result = await _loginUseCase.call(
        LoginParams(email: email, password: password),
      );
      result.fold(
        (failure) => _handleError(failure),
        (user) => _handleSuccess(user),
      );
    } finally {
      _isLoading.value = false;
    }
  }
}
```

### 3.3. GetX 路由管理

#### 声明式路由配置
```dart
// app_pages.dart
class AppPages {
  static const INITIAL = Routes.SPLASH;
  
  static final routes = [
    GetPage(
      name: Routes.LOGIN,
      page: () => const LoginPage(),
      binding: AuthBinding(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: Routes.HOME,
      page: () => const HomePage(),
      binding: HomeBinding(),
      middlewares: [AuthMiddleware()],
    ),
  ];
}

// app_routes.dart
abstract class Routes {
  static const SPLASH = '/splash';
  static const LOGIN = '/login';
  static const HOME = '/home';
  static const PROFILE = '/profile';
}
```

#### 路由中间件
```dart
// middlewares.dart
class AuthMiddleware extends GetMiddleware {
  @override
  RouteSettings? redirect(String? route) {
    final authController = Get.find<AuthController>();
    
    if (!authController.isAuthenticated && route != Routes.LOGIN) {
      return const RouteSettings(name: Routes.LOGIN);
    }
    
    if (authController.isAuthenticated && route == Routes.LOGIN) {
      return const RouteSettings(name: Routes.HOME);
    }
    
    return null;
  }
}
```

### 3.4. 状态持久化和缓存
*   **GetStorage**: 轻量级本地存储解决方案
*   **Hive**: 高性能本地数据库
*   **Secure Storage**: 敏感信息安全存储
*   **Cache Management**: 网络数据智能缓存

### 3.5. UI 组件设计

#### 设计系统
*   统一的主题配置
*   可复用的组件库
*   响应式设计适配
*   无障碍访问支持

#### GetX 组件模式
*   **GetView<T>**: 自动注入控制器的页面组件
*   **GetWidget<T>**: 可复用的响应式组件
*   **GetBuilder**: 手动控制更新的组件
*   **Obx**: 简单响应式组件包装器

```dart
// GetView 示例
class LoginPage extends GetView<AuthController> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Obx(() => controller.isLoading
        ? const Center(child: CircularProgressIndicator())
        : LoginForm(),
      ),
    );
  }
}
```

### 3.6. 数据流示例 (GetX 用户登录)

1. **UI Event**: 用户在 `LoginForm` 中点击登录按钮
2. **Controller Method**: 调用 `AuthController.login()` 方法
3. **Use Case**: 控制器调用 `LoginUserUseCase`
4. **Repository**: 通过 `AuthRepository` 接口调用数据层
5. **Data Source**: `AuthRemoteDataSource` 发送网络请求
6. **Reactive Update**: 使用 `.obs` 变量自动更新状态
7. **UI Rebuild**: `Obx` 组件自动响应状态变化重新构建

## 4. 共享代码包 (`packages/shared`)

为了确保前后端之间的数据一致性和代码复用，所有共享的类型定义、验证规则和工具函数都在此包中定义。

### 4.1. 单一事实来源与代码生成 (Single Source of Truth)

**核心原则**: 项目中所有的数据交换模型（DTOs）和 API 接口定义，其**唯一的“事实来源”是位于 `packages/shared/schemas/` 目录下的 OpenAPI 规范文件 (openapi.yaml)。**

任何数据模型的变更，都必须从修改此 OpenAPI 文件开始，然后通过自动化脚本生成前后端所需的代码。

**工作流程**:
1.  **定义/修改契约**: 在 `openapi.yaml` 中定义或修改 API 路径、请求/响应模型。
2.  **生成代码**: 在根目录运行一个统一的脚本 (例如 `make generate-api` 或 `npm run codegen`)。
3.  **自动化执行**: 该脚本会触发 `openapi-generator` 或类似工具，执行以下操作：
    - 为**后端 (FastAPI)**：生成 Pydantic 模型 (`schema.py`) 到相应的 `features` 目录。
    - 为**前端 (Flutter)**：生成 Dart 数据类 (包含 `fromJson`/`toJson`) 到 `features` 的 `models` 目录。
4.  **使用代码**: 开发人员在各自的代码中直接导入并使用这些自动生成的、类型安全的数据模型。

**禁止行为**: 严禁手动在前端或后端代码中创建或修改 API 数据模型。所有模型必须由代码生成器统一管理。


```
packages/shared/
├── lib/
│   └── src/
│       ├── dtos/             # 数据传输对象
│       │   ├── auth/
│       │   │   ├── login_request_dto.dart
│       │   │   ├── login_response_dto.dart
│       │   │   └── user_dto.dart
│       │   └── common/
│       │       ├── api_response_dto.dart
│       │       └── pagination_dto.dart
│       ├── enums/            # 枚举类型
│       │   ├── user_role.dart
│       │   ├── auth_status.dart
│       │   └── api_status.dart
│       ├── validators/       # 验证规则
│       │   ├── email_validator.dart
│       │   ├── password_validator.dart
│       │   └── common_validators.dart
│       ├── constants/        # 常量定义
│       │   ├── api_constants.dart
│       │   ├── error_codes.dart
│       │   └── app_constants.dart
│       ├── utils/           # 通用工具函数
│       │   ├── date_utils.dart
│       │   ├── string_utils.dart
│       │   └── crypto_utils.dart
│       └── index.dart       # 导出文件
├── codegen/                 # 代码生成配置
│   ├── build.yaml
│   ├── generator_config.yaml
│   └── templates/
│       ├── dto_template.dart
│       └── validator_template.dart
├── schemas/                 # API 模式定义
│   ├── openapi.yaml
│   ├── json_schemas/
│   │   ├── user_schema.json
│   │   └── auth_schema.json
│   └── graphql/
│       └── schema.graphql
├── package.json            # Node.js 包配置
└── pubspec.yaml           # Dart 包配置
```

### 4.1. 代码生成和同步

#### 自动化代码生成
*   使用 `json_serializable` 生成 Dart 序列化代码
*   使用 `quicktype` 生成多语言类型定义
*   使用 `openapi-generator` 生成 API 客户端代码

#### 类型安全保证
*   编译时类型检查
*   运行时数据验证
*   API 契约测试

#### 版本管理
*   语义化版本控制
*   向后兼容性检查
*   自动化版本发布

### 4.2. 使用示例

```dart
// 前端使用
import 'package:shared/shared.dart';

final loginRequest = LoginRequestDto(
  email: email,
  password: password,
);

// 后端使用 (Python)
from shared.dtos.auth import LoginRequestDto

login_request = LoginRequestDto.from_dict(request_data)
```

## 5. 测试策略

### 5.1. 测试金字塔

#### 单元测试 (70%)
*   业务逻辑测试
*   工具函数测试
*   数据模型测试
*   用例测试

#### 集成测试 (20%)
*   API 端点测试
*   数据库操作测试
*   外部服务集成测试
*   组件间交互测试

#### 端到端测试 (10%)
*   完整用户流程测试
*   跨服务测试
*   UI 自动化测试
*   性能测试

### 5.2. 测试工具和框架

#### 后端测试
*   **pytest**: 测试框架
*   **pytest-asyncio**: 异步测试支持
*   **httpx**: HTTP 客户端测试
*   **factory_boy**: 测试数据生成
*   **pytest-cov**: 覆盖率报告

#### 前端测试
*   **flutter_test**: Flutter 测试框架
*   **mockito**: Mock 对象生成
*   **get_test**: GetX 控制器测试工具
*   **integration_test**: 集成测试支持
*   **golden_toolkit**: 黄金测试（UI 快照测试）
*   **patrol**: 高级集成测试框架

### 5.3. 持续集成测试

```yaml
# .github/workflows/test.yml
name: Test

on: [push, pull_request]

jobs:
  backend-test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'
      - name: Install dependencies
        run: |
          cd cheestack-fastapi
          pip install -r requirements.txt
      - name: Run tests
        run: |
          cd cheestack-fastapi
          pytest --cov=. --cov-report=xml
      - name: Upload coverage
        uses: codecov/codecov-action@v3

  frontend-test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Set up Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.16.0'
      - name: Install dependencies
        run: |
          cd cheestack-flt
          flutter pub get
      - name: Run tests
        run: |
          cd cheestack-flt
          flutter test --coverage
      - name: Upload coverage
        uses: codecov/codecov-action@v3
```

## 6. 部署和 DevOps

### 6.1. 容器化配置

#### 后端 Dockerfile
```dockerfile
# cheestack-fastapi/docker/Dockerfile
FROM python:3.11-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    libpq-dev \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt ./
COPY pyproject.toml ./

# 安装Python依赖
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . ./

# 创建非root用户
RUN useradd --create-home --shell /bin/bash app && \
    chown -R app:app /app
USER app

EXPOSE 8000

CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

#### 前端构建（Web版本）
```dockerfile
# cheestack-flt/docker/Dockerfile.web
FROM nginx:alpine

COPY build/web /usr/share/nginx/html
COPY docker/nginx.conf /etc/nginx/nginx.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
```

### 6.2. Kubernetes 部署

```yaml
# cheestack-fastapi/k3s/backend.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: cheestack-backend
  namespace: cheestack
spec:
  replicas: 2
  selector:
    matchLabels:
      app: cheestack-backend
  template:
    metadata:
      labels:
        app: cheestack-backend
    spec:
      containers:
      - name: backend
        image: cheestack/fastapi:latest
        ports:
        - containerPort: 8000
        envFrom:
        - configMapRef:
            name: cheestack-config
        env:
        - name: DATABASE_USER
          valueFrom:
            secretKeyRef:
              name: cheestack-secret
              key: DATABASE_USER
        - name: DATABASE_PASSWORD
          valueFrom:
            secretKeyRef:
              name: cheestack-secret
              key: DATABASE_PASSWORD
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /api/v1/health/
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /api/v1/health/
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
```

### 6.3. 监控和日志

#### 监控栈
*   **Prometheus**: 指标收集
*   **Grafana**: 可视化面板
*   **AlertManager**: 告警管理
*   **Jaeger**: 分布式追踪

#### 日志管理
*   **ELK Stack**: Elasticsearch, Logstash, Kibana
*   **Fluentd**: 日志收集代理
*   结构化日志格式
*   日志等级管理

## 7. 开发工具和代码质量

### 7.1. 代码质量工具

#### Pre-commit 配置
```yaml
# .pre-commit-config.yaml
repos:
  - repo: https://github.com/psf/black
    rev: 23.3.0
    hooks:
      - id: black
        language_version: python3.11

  - repo: https://github.com/pycqa/isort
    rev: 5.12.0
    hooks:
      - id: isort

  - repo: https://github.com/pycqa/flake8
    rev: 6.0.0
    hooks:
      - id: flake8

  - repo: local
    hooks:
      - id: dart-format
        name: dart format
        entry: dart format
        language: system
        files: \.dart$
```

#### 代码分析配置
```yaml
# analysis_options.yaml
include: package:flutter_lints/flutter.yaml

analyzer:
  exclude:
    - "**/*.g.dart"
    - "**/*.freezed.dart"
  strong-mode:
    implicit-casts: false
    implicit-dynamic: false

linter:
  rules:
    prefer_single_quotes: true
    sort_constructors_first: true
    sort_unnamed_constructors_first: true
    always_use_package_imports: true
```

### 7.2. IDE 配置

#### VS Code 设置
```json
{
  ".vscode/settings.json": {
    "python.defaultInterpreterPath": "./packages/backend/.venv/bin/python",
    "python.formatting.provider": "black",
    "python.linting.enabled": true,
    "python.linting.flake8Enabled": true,
    "dart.flutterSdkPath": "/path/to/flutter",
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
      "source.organizeImports": true
    }
  }
}
```

## 8. 安全最佳实践

### 8.1. 认证和授权

#### JWT 配置
*   访问令牌短期有效期（15分钟）
*   刷新令牌长期有效期（7天）
*   令牌轮换机制
*   安全的令牌存储

#### 权限管理
*   基于角色的访问控制 (RBAC)
*   细粒度权限定义
*   权限缓存机制
*   审计日志记录

### 8.2. 数据保护

#### 敏感数据处理
*   密码哈希 (bcrypt)
*   数据加密 (AES-256)
*   PII 数据脱敏
*   GDPR 合规性

#### API 安全
*   输入验证和清理
*   SQL 注入防护
*   XSS 攻击防护
*   CSRF 保护

### 8.3. 网络安全

*   HTTPS 强制使用
*   CORS 策略配置
*   API 限流和防 DDoS
*   安全头部配置
*   IP 白名单机制

## 9. 性能优化

### 9.1. 后端优化

#### 数据库配置和连接管理 (Tortoise ORM)
```python
# app/core/database.py
from tortoise import Tortoise
from tortoise.contrib.fastapi import register_tortoise
from app.core.settings import get_settings

settings = get_settings()

TORTOISE_ORM = {
    "connections": {
        "default": {
            "engine": "tortoise.backends.asyncpg",
            "credentials": {
                "host": settings.DATABASE_HOST,
                "port": settings.DATABASE_PORT,
                "user": settings.DATABASE_USER,
                "password": settings.DATABASE_PASSWORD,
                "database": settings.DATABASE_NAME,
            }
        }
    },
    "apps": {
        "models": {
            "models": [
                "app.features.auth.models",
                "app.features.user.models",
                "aerich.models"
            ],
            "default_connection": "default",
        }
    },
}

async def init_database():
    """初始化数据库连接"""
    await Tortoise.init(config=TORTOISE_ORM)
    
async def generate_schemas():
    """生成数据库表结构"""
    await Tortoise.generate_schemas()

async def close_database():
    """关闭数据库连接"""
    await Tortoise.close_connections()

def register_database(app):
    """注册数据库到 FastAPI 应用"""
    register_tortoise(
        app,
        config=TORTOISE_ORM,
        generate_schemas=settings.DEBUG,  # 仅在开发环境自动生成表
        add_exception_handlers=True,
    )
```

#### 数据库工具函数
```python
# app/core/db_utils.py (可选的数据库工具文件)
from tortoise.transactions import in_transaction
from typing import Optional, Dict, Any
from tortoise.models import Model

async def get_or_create(model_class: Model, defaults: Optional[Dict[str, Any]] = None, **kwargs):
    """获取或创建模型实例"""
    try:
        instance = await model_class.get(**kwargs)
        return instance, False
    except model_class.DoesNotExist:
        create_kwargs = {**kwargs, **(defaults or {})}
        instance = await model_class.create(**create_kwargs)
        return instance, True

async def bulk_create_or_update(model_class: Model, data_list: list, update_fields: list = None):
    """批量创建或更新"""
    async with in_transaction() as conn:
        # 批量操作逻辑
        pass

class DatabaseManager:
    """数据库管理器 - 处理连接池、事务等"""
    
    @staticmethod
    async def health_check() -> bool:
        """数据库健康检查"""
        try:
            await Tortoise.get_connection("default").execute_query("SELECT 1")
            return True
        except Exception:
            return False
    
    @staticmethod
    async def get_connection_info():
        """获取连接信息"""
        conn = Tortoise.get_connection("default")
        return {
            "engine": conn.__class__.__name__,
            "database": conn.database,
        }
```

```python
# app/main.py - 应用启动时的数据库初始化
from fastapi import FastAPI
from app.core.database import register_database, close_database

app = FastAPI()

# 注册数据库
register_database(app)

@app.on_event("shutdown")
async def shutdown_event():
    await close_database()
```

### 职责分工说明

**`database.py`** - 数据库配置和连接管理：
- Tortoise ORM 配置定义
- 数据库连接初始化和关闭
- FastAPI 应用注册
- 连接池管理

**`db_utils.py`** (可选) - 数据库工具函数：
- 通用数据库操作工具
- 事务管理辅助函数
- 数据库健康检查
- 批量操作工具

**`models.py`** (在各 feature 中) - 数据模型定义：
- Tortoise ORM 模型类
- 字段定义和约束
- 模型方法和属性
- 关系定义
#### 迁移管理 (Aerich)
```bash
# 初始化迁移
aerich init -t app.core.database.TORTOISE_ORM

# 生成迁移文件
aerich migrate --name "add_user_table"

# 执行迁移
aerich upgrade

# 回滚迁移
aerich downgrade
```

#### API 性能
*   响应压缩 (gzip)
*   分页机制
*   异步处理
*   CDN 配置

### 9.2. 前端优化

#### GetX 性能优化
*   **GetX Controller**: 智能依赖管理和内存回收
*   **LazyPut**: 懒加载依赖注入，减少启动时间
*   **GetBuilder**: 精确控制 UI 更新范围
*   **Obx vs GetBuilder**: 根据场景选择合适的响应式方案

```dart
// 性能优化示例
class AuthController extends GetxController {
  // 使用 Worker 优化复杂逻辑
  @override
  void onInit() {
    super.onInit();
    
    // 防抖处理搜索
    debounce(_searchQuery, _performSearch, time: Duration(milliseconds: 500));
    
    // 监听用户状态变化
    ever(_user, _onUserChanged);
  }
  
  // 使用 GetBuilder 优化特定区域更新
  void updateSpecificArea() {
    update(['user_info']); // 只更新特定 ID 的组件
  }
}
```

#### 用户体验
*   骨架屏加载
*   错误边界处理
*   离线支持
*   渐进式 Web 应用 (PWA)

## 10. 文档和协作

### 10.1. 文档标准

#### API 文档
*   OpenAPI/Swagger 规范
*   自动化文档生成
*   示例代码和用例
*   版本控制和变更日志
