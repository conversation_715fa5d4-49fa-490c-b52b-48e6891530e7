part of application_page;

class ApplicationController extends GetxController {
  late final PageController pageController;

  AppVersionModel? appVersionModel;

  int currentPage = 0;

  @override
  void onInit() {
    super.onInit();
    pageController = PageController(initialPage: currentPage);
    init();
  }

  @override
  void onReady() async {
    await SupgradeController.to.checkUpdate();
    super.onReady();
  }

  void onPageChanged(int page) {
    currentPage = page;
    switch (page) {
      case 0:
        break;
      case 1:
        break;
      default:
    }
    update();
  }

  @override
  void onClose() {
    pageController.dispose();
    super.onClose();
  }

  void init() async {
    await Global.initGromore();

    // 懒加载控制器
    Get.lazyPut<HomeController>(() => HomeController());
    Get.lazyPut<ProfileController>(() => ProfileController());
    Get.lazyPut<CreationController>(() => CreationController());
    Get.lazyPut<ListeningController>(() => ListeningController());

    // 初始化这些contooller
    Get.find<HomeController>();
    Get.find<ProfileController>();
    Get.find<CreationController>();
    Get.find<ListeningController>();

    // StudyController 现在由 StudyPage 自己管理，不需要在这里初始化

    // 延迟执行BookDataService测试
    Future.delayed(const Duration(seconds: 3), () {
      _testBookDataService();
    });
  }

  /// 测试BookDataService功能
  Future<void> _testBookDataService() async {
    Console.log('=== ApplicationController: 开始测试BookDataService功能 ===');

    try {
      // 检查服务是否可用
      Console.log(
          '检查BookDataService是否注册: ${Get.isRegistered<BookDataService>()}');
      Console.log(
          '检查UserDataService是否注册: ${Get.isRegistered<UserDataService>()}');

      if (!Get.isRegistered<BookDataService>()) {
        Console.log('❌ BookDataService未注册');
        return;
      }

      final bookDataService = BookDataService.to;
      final userDataService = UserDataService.to;

      Console.log('当前用户: ${userDataService.currentUser?.username}');
      Console.log('用户ID: ${userDataService.currentUser?.id}');

      if (userDataService.currentUser == null) {
        Console.log('❌ 用户未登录，无法测试');
        return;
      }

      // 测试创建书籍
      Console.log('开始测试创建书籍...');
      final result = await bookDataService.createBook(
        name: '应用测试书籍_${DateTime.now().millisecondsSinceEpoch}',
        brief: '这是一个从应用控制器创建的测试书籍',
        privacy: 'private',
      );

      if (result != null) {
        Console.log('✅ 书籍创建成功: ${result.name} (ID: ${result.id})');
        ShowToast.success('测试成功：书籍创建完成');
      } else {
        Console.log('❌ 书籍创建失败');
        ShowToast.fail('测试失败：书籍创建失败');
      }
    } catch (e, stackTrace) {
      Console.log('❌ 测试过程中发生错误: $e');
      Console.log('错误堆栈: $stackTrace');
      ShowToast.fail('测试失败：$e');
    }

    Console.log('=== ApplicationController: BookDataService功能测试完成 ===');
  }
}
