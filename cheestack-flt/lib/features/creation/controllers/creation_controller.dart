import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'package:cheestack_flt/apis/index.dart';
import 'package:cheestack_flt/common/enums/index.dart';
import 'package:cheestack_flt/features/auth/controllers/controller.dart';
import 'package:cheestack_flt/shared/extensions/index.dart';
import 'package:cheestack_flt/services/index.dart';

import 'package:cheestack_flt/models/index.dart';
import 'package:cheestack_flt/routes/index.dart';
import 'package:cheestack_flt/shared/utils/oxhttp/index.dart';
import 'package:cheestack_flt/shared/utils/index.dart';
import 'package:cheestack_flt/widgets/index.dart';
import '../apis/book_service.dart';
import '../apis/card_service.dart';

class CreationController extends GetxController {
  final BookService _bookService = BookService();
  final CardService _cardService = CardService();

  /// 创作统计数据
  int totalBooks = 0;
  int totalCards = 0;
  int todayCreations = 0;
  int weekCreations = 0;

  /// 最近创作列表
  List<Map<String, dynamic>> recentCreations = [];

  /// 书籍管理相关
  List<BookModel> bookList = [];
  List<BookModel> filteredBookList = [];
  String bookSearchKeyword = '';

  /// 书籍筛选和排序相关
  String selectedPrivacyFilter = 'all'; // all, public, private
  String selectedTimeFilter = 'all'; // all, today, week, month
  String selectedSortOption =
      'created_desc'; // created_desc, created_asc, updated_desc, updated_asc, name_asc, name_desc
  bool isFilterActive = false;
  bool isGridView = false; // 列表/网格视图切换

  /// 卡片管理相关
  List<CardModel> cardList = [];
  String cardSearchKeyword = '';

  /// 同步相关状态
  bool isSyncing = false;
  String syncStatus = 'idle'; // idle, syncing, success, failed
  double syncProgress = 0.0;
  String? syncErrorMessage;

  /// 输入框控制器
  TextEditingController textEditingController = TextEditingController();

  /// 输入框内容改变回调
  Function(String)? onInputValueChanged;

  CreationController();

  Future init() async {
    await loadCreationStats();
    update();
  }

  void onTap() {}

  /// 加载创作统计数据
  Future<void> loadCreationStats() async {
    await _loadCreationStats();
    await _loadRecentCreations();
    update();
  }

  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() async {
    super.onReady();
    await loadCreationStats();
  }

  /// 加载创作统计数据 - 使用专门的统计接口
  Future<void> _loadCreationStats() async {
    await asyncRequest(
      () => OxHttp.to.get(HttpUrl.creationStats),
      onSuccess: (data) {
        print('📊 加载创作统计数据成功: $data');

        // 设置统计数据
        totalBooks = data['total_books'] ?? 0;
        totalCards = data['total_cards'] ?? 0;
        todayCreations = data['today_creations'] ?? 0;
        weekCreations = data['week_creations'] ?? 0;

        print(
            '📊 创作统计: 书籍=${totalBooks}, 卡片=${totalCards}, 今日=${todayCreations}, 本周=${weekCreations}');
      },
      onFailure: (error) {
        print('❌ 加载创作统计失败: ${error?.msg ?? "未知错误"}');
        ShowToast.fail("加载创作统计失败: ${error?.msg ?? "未知错误"}");
      },
    );
  }

  /// 加载最近创作数据 - 只显示最近3条卡片
  Future<void> _loadRecentCreations() async {
    recentCreations.clear();

    final userId = AuthController.to.usr.user?.id;
    print('👤 当前用户ID: $userId');

    if (userId == null) {
      print('❌ 用户未登录，无法加载卡片');
      return;
    }

    Map<String, dynamic> cardFilters = {
      "user_id": userId
    };

    print('🔍 查询参数: $cardFilters');

    // 只获取最近的3条卡片
    await asyncRequest(
      () => OxHttp.to.get(HttpUrl.cards, queryParameters: {
        "skip": 0,
        "limit": 3,
        "order": "-created_at",
        "filters": jsonEncode(cardFilters),
      }),
      onSuccess: (data) {
        print('🃏 加载最近卡片数据成功，数量: ${data.length}');
        print('🔍 原始数据: $data');

        for (var item in data) {
          try {
            print('📝 处理卡片: ${item['title']} - ${item['created_at']}');

            // 处理 created_at 为 null 的情况
            String timeStr = '未知时间';
            if (item['created_at'] != null) {
              try {
                timeStr = _formatTime(item['created_at']);
              } catch (e) {
                print('⚠️ 时间格式化失败: $e');
                timeStr = '时间格式错误';
              }
            }

            recentCreations.add({
              'type': 'card',
              'id': item['id'],
              'title': item['title'] ?? '未命名卡片',
              'subtitle': item['question'] ?? '暂无问题',
              'time': timeStr,
              'data': item,
            });

            print('✅ 卡片处理成功: ${item['title']}');
          } catch (e) {
            print('❌ 处理卡片失败: ${item['title']} - 错误: $e');
          }
        }

        print('✅ 最近创作卡片加载完成，数量: ${recentCreations.length}');
        for (var item in recentCreations) {
          print('   - ${item['title']} (${item['time']})');
        }

        // 立即更新UI
        update();
      },
      onFailure: (error) {
        print('❌ 加载卡片数据失败: ${error?.msg ?? "未知错误"}');
      },
    );
  }

  /// 格式化时间显示
  String _formatTime(String timeStr) {
    final time = DateTime.parse(timeStr);
    final now = DateTime.now();
    final diff = now.difference(time);

    if (diff.inDays > 0) {
      return '${diff.inDays}天前';
    } else if (diff.inHours > 0) {
      return '${diff.inHours}小时前';
    } else if (diff.inMinutes > 0) {
      return '${diff.inMinutes}分钟前';
    } else {
      return '刚刚';
    }
  }

  /// 导航方法
  Future<void> toBookCreationPage() async {
    await Get.toNamed(AppRoutes.creationBookList);
  }

  Future<void> toCardCreationPage() async {
    await Get.toNamed(AppRoutes.creationCardList);
  }

  /// 书籍管理方法
  Future<void> loadBookList() async {
    try {
      bookList = await _bookService.getBookList(
        search: bookSearchKeyword.isEmpty ? null : bookSearchKeyword,
      );
      _applyFiltersAndSort();
      update();
    } catch (e) {
      ShowToast.fail('加载书籍列表失败: $e');
    }
  }

  void onBookSearchChanged(String keyword) {
    bookSearchKeyword = keyword;
    loadBookList();
  }

  /// 应用筛选和排序
  void _applyFiltersAndSort() {
    List<BookModel> filtered = List.from(bookList);

    // 应用隐私筛选
    if (selectedPrivacyFilter != 'all') {
      filtered = filtered.where((book) {
        return book.privacy == selectedPrivacyFilter;
      }).toList();
    }

    // 应用时间筛选
    if (selectedTimeFilter != 'all') {
      final now = DateTime.now();
      filtered = filtered.where((book) {
        if (book.createdAt == null) return false;
        final createdAt = DateTime.parse(book.createdAt!);

        switch (selectedTimeFilter) {
          case 'today':
            final today = DateTime(now.year, now.month, now.day);
            return createdAt.isAfter(today);
          case 'week':
            final weekStart = now.subtract(Duration(days: now.weekday - 1));
            final weekStartDay =
                DateTime(weekStart.year, weekStart.month, weekStart.day);
            return createdAt.isAfter(weekStartDay);
          case 'month':
            final monthStart = DateTime(now.year, now.month, 1);
            return createdAt.isAfter(monthStart);
          default:
            return true;
        }
      }).toList();
    }

    // 应用排序
    filtered.sort((a, b) {
      switch (selectedSortOption) {
        case 'created_desc':
          return _compareDateTime(b.createdAt, a.createdAt);
        case 'created_asc':
          return _compareDateTime(a.createdAt, b.createdAt);
        case 'updated_desc':
          return _compareDateTime(b.updatedAt, a.updatedAt);
        case 'updated_asc':
          return _compareDateTime(a.updatedAt, b.updatedAt);
        case 'name_asc':
          return (a.name ?? '').compareTo(b.name ?? '');
        case 'name_desc':
          return (b.name ?? '').compareTo(a.name ?? '');
        default:
          return _compareDateTime(b.createdAt, a.createdAt);
      }
    });

    filteredBookList = filtered;
    _updateFilterActiveStatus();
  }

  /// 比较日期时间字符串
  int _compareDateTime(String? a, String? b) {
    if (a == null && b == null) return 0;
    if (a == null) return 1;
    if (b == null) return -1;

    try {
      final dateA = DateTime.parse(a);
      final dateB = DateTime.parse(b);
      return dateA.compareTo(dateB);
    } catch (e) {
      return 0;
    }
  }

  /// 更新筛选激活状态
  void _updateFilterActiveStatus() {
    isFilterActive = selectedPrivacyFilter != 'all' ||
        selectedTimeFilter != 'all' ||
        selectedSortOption != 'created_desc' ||
        bookSearchKeyword.isNotEmpty;
  }

  /// 筛选和排序控制方法
  void setPrivacyFilter(String filter) {
    selectedPrivacyFilter = filter;
    _applyFiltersAndSort();
    update();
  }

  void setTimeFilter(String filter) {
    selectedTimeFilter = filter;
    _applyFiltersAndSort();
    update();
  }

  void setSortOption(String sortOption) {
    selectedSortOption = sortOption;
    _applyFiltersAndSort();
    update();
  }

  void toggleViewMode() {
    isGridView = !isGridView;
    update();
  }

  void clearAllFilters() {
    selectedPrivacyFilter = 'all';
    selectedTimeFilter = 'all';
    selectedSortOption = 'created_desc';
    bookSearchKeyword = '';
    _applyFiltersAndSort();
    update();
  }

  /// 获取筛选选项的显示文本
  String getPrivacyFilterText(String filter) {
    switch (filter) {
      case 'public':
        return '公开';
      case 'private':
        return '私有';
      default:
        return '全部';
    }
  }

  String getTimeFilterText(String filter) {
    switch (filter) {
      case 'today':
        return '今天';
      case 'week':
        return '本周';
      case 'month':
        return '本月';
      default:
        return '全部时间';
    }
  }

  String getSortOptionText(String sortOption) {
    switch (sortOption) {
      case 'created_desc':
        return '创建时间↓';
      case 'created_asc':
        return '创建时间↑';
      case 'updated_desc':
        return '更新时间↓';
      case 'updated_asc':
        return '更新时间↑';
      case 'name_asc':
        return '名称A-Z';
      case 'name_desc':
        return '名称Z-A';
      default:
        return '创建时间↓';
    }
  }

  /// 加载更多书籍
  Future<List<BookModel>> loadMoreBooks() async {
    return await _bookService.getBookList(
      skip: bookList.length,
      search: bookSearchKeyword.isEmpty ? null : bookSearchKeyword,
    );
  }

  Future<void> createBook() async {
    final result = await Get.toNamed(AppRoutes.creationBookEdit);
    if (result != null) {
      await loadBookList();
      await loadCreationStats();
    }
  }

  Future<void> editBook(BookModel book) async {
    final result =
        await Get.toNamed(AppRoutes.creationBookEdit, arguments: book);
    if (result != null) {
      await loadBookList();
      await loadCreationStats();
    }
  }

  Future<void> deleteBook(BookModel book) async {
    final confirmed = await _showDeleteBookDialog(book);
    if (!confirmed) return;

    try {
      await _bookService.deleteBook(book.id!);
      bookList.removeWhere((b) => b.id == book.id);
      ShowToast.success('删除成功');
      await loadCreationStats();
      update();
    } catch (e) {
      ShowToast.fail('删除失败: $e');
    }
  }

  Future<void> viewBookDetail(BookModel book) async {
    await Get.toNamed(AppRoutes.creationBookDetail, arguments: book);
  }

  Future<void> manageBookCards(BookModel book) async {
    await Get.toNamed(AppRoutes.creationCardList,
        arguments: {'bookId': book.id});
  }

  Future<void> duplicateBook(BookModel book) async {
    try {
      final bookData = {
        'name': '${book.name} - 副本',
        'brief': book.brief,
        'privacy': book.privacy,
      };

      await _bookService.createBook(bookData, book.cover);
      ShowToast.success('复制成功');
      await loadBookList();
      await loadCreationStats();
    } catch (e) {
      ShowToast.fail('复制失败: $e');
    }
  }

  Future<bool> _showDeleteBookDialog(BookModel book) async {
    return await Get.dialog<bool>(
          AlertDialog(
            title: const OxText('确认删除'),
            content: OxText('确定要删除书籍"${book.name}"吗？此操作不可撤销。'),
            actions: [
              TextButton(
                onPressed: () => Get.back(result: false),
                child: const OxText('取消'),
              ),
              TextButton(
                onPressed: () => Get.back(result: true),
                style: TextButton.styleFrom(
                  foregroundColor: Theme.of(Get.context!).colorScheme.error,
                ),
                child: const OxText('删除'),
              ),
            ],
          ),
        ) ??
        false;
  }

  /// 卡片管理方法
  Future<void> loadCardList({int? bookId, String? cardType}) async {
    try {
      cardList = await _cardService.getCardList(
        search: cardSearchKeyword.isEmpty ? null : cardSearchKeyword,
        bookId: bookId,
        cardType: cardType,
      );
      update();
    } catch (e) {
      ShowToast.fail('加载卡片列表失败: $e');
    }
  }

  void onCardSearchChanged(String keyword) {
    cardSearchKeyword = keyword;
    loadCardList();
  }

  /// 加载更多卡片
  Future<List<CardModel>> loadMoreCards({int? bookId}) async {
    return await _cardService.getCardList(
      skip: cardList.length,
      search: cardSearchKeyword.isEmpty ? null : cardSearchKeyword,
      bookId: bookId,
    );
  }

  Future<void> createCard({int? bookId}) async {
    final result = await Get.toNamed(AppRoutes.creationCardEdit,
        arguments: {'bookId': bookId});
    if (result != null) {
      await loadCardList(bookId: bookId);
      await loadCreationStats();
    }
  }

  Future<void> editCard(CardModel card) async {
    final result = await Get.toNamed(AppRoutes.creationCardEdit,
        arguments: {'card': card});
    if (result != null) {
      await loadCardList();
      await loadCreationStats();
    }
  }

  Future<void> deleteCard(CardModel card) async {
    final confirmed = await _showDeleteCardDialog(card);
    if (!confirmed) return;

    try {
      await _cardService.deleteCard(card.id!);
      cardList.removeWhere((c) => c.id == card.id);
      ShowToast.success('删除成功');
      await loadCreationStats();
      update();
    } catch (e) {
      ShowToast.fail('删除失败: $e');
    }
  }

  Future<void> viewCardDetail(CardModel card) async {
    await Get.toNamed('/creation/cards/detail', arguments: card);
  }

  Future<void> duplicateCard(CardModel card) async {
    try {
      final cardData = CardModelCreate(
        bookId: null, // CardModel没有bookId字段，复制时不关联书籍
        type: card.type,
        typeVersion: card.typeVersion,
        title: '${card.title} - 副本',
        question: card.question,
        answer: card.answer,
        extra: card.extra as Map<String, dynamic>?,
        cardAssets: card.cardAssets ?? [],
      );

      await _cardService.createCard(cardData);
      ShowToast.success('复制成功');
      await loadCardList();
      await loadCreationStats();
    } catch (e) {
      ShowToast.fail('复制失败: $e');
    }
  }

  Future<void> removeCardFromBook(CardModel card) async {
    try {
      await _cardService.removeCardFromBook(card.id!);
      ShowToast.success('已从书籍中移除');
      await loadCardList();
    } catch (e) {
      ShowToast.fail('移除失败: $e');
    }
  }

  Future<bool> _showDeleteCardDialog(CardModel card) async {
    return await Get.dialog<bool>(
          AlertDialog(
            title: const OxText('确认删除'),
            content: OxText('确定要删除卡片"${card.title}"吗？此操作不可撤销。'),
            actions: [
              TextButton(
                onPressed: () => Get.back(result: false),
                child: const OxText('取消'),
              ),
              TextButton(
                onPressed: () => Get.back(result: true),
                style: TextButton.styleFrom(
                  foregroundColor: Theme.of(Get.context!).colorScheme.error,
                ),
                child: const OxText('删除'),
              ),
            ],
          ),
        ) ??
        false;
  }

  Future<void> toAICardCreationPage() async {
    // TODO: 实现AI创作卡片页面
    ShowToast.success("AI创作卡片功能即将上线");
  }

  Future<void> toCreationListPage() async {
    // TODO: 实现创作列表页面
    ShowToast.success("创作列表页面即将上线");
  }

  Future<void> toCreationSettings() async {
    // TODO: 实现创作设置页面
    ShowToast.success("创作设置页面即将上线");
  }

  /// 打开最近创作项目 - 只处理卡片
  void openRecentCreation(Map<String, dynamic> item) {
    if (item['type'] == 'card') {
      final cardModel = CardModel.fromJson(item['data']);
      toCardDetailPage(cardModel: cardModel);
    }
  }

  /// 跳转到卡片详情页面
  Future<void> toCardDetailPage({required CardModel cardModel}) async {
    await Get.toNamed(AppRoutes.review, arguments: {
      "type": StudyType.info,
      "cardModel": cardModel,
    });
  }

  // 调试方法
  void debugPrintStatus() {
    print('🔍 调试信息:');
    print('   - 总书籍数: $totalBooks');
    print('   - 总卡片数: $totalCards');
    print('   - 最近创作数量: ${recentCreations.length}');
    print('   - 最近创作内容: $recentCreations');
    print('   - 用户ID: ${AuthController.to.usr.user?.id}');
  }

  // 手动刷新数据
  Future<void> manualRefresh() async {
    print('🔄 手动刷新数据...');
    await loadCreationStats();
    ShowToast.success('数据刷新完成');
  }

  // 保留原有的一些方法用于兼容性

  /// 刷新数据
  Future<void> onRefresh() async {
    await loadCreationStats();
  }

  Future toBookEditPage({BookModel? bookModel}) async {
    await Get.toNamed(AppRoutes.bookEditor, arguments: bookModel)
        ?.then((e) async {
      await onRefresh();
    }).then((e) {
      update();
    });
  }

  Future toBookInfoPage({BookModel? bookModel}) async {
    await Get.toNamed(AppRoutes.bookInfo, arguments: bookModel)
        ?.then((e) async {
      await onRefresh();
    }).then((e) {
      update();
    });
  }

  onDeleteBook(BookModel bookModel) {
    Sdialog.show(
      cancel: const OxText("取消"),
      onCancel: () => Get.back(),
      confirm: const OxText("确认"),
      onConfirm: () async {
        await asyncRequest(
          () => OxHttp.to.delete("${HttpUrl.books}/${bookModel.id}"),
          onSuccess: (data) {
            // 删除成功后刷新统计数据
            loadCreationStats();
            ShowToast.success("删除成功");
          },
          onFailure: (error) {
            ShowToast.fail(error?.msg ?? "未知错误");
          },
        );
        Get.back();
      },
      builder: (BuildContext context) {
        return const OxText("确定删除吗? 该操作不可逆");
      },
    );
  }

  selectEditingBook(int id) async {
    await Get.toNamed(AppRoutes.cardEditList, arguments: id);
  }

  Future<void> updateConfigModel() async {
    await asyncRequest(
      () => OxHttp.to
          .put(HttpUrl.config, data: AuthController.to.usr.config?.toJson()),
      onSuccess: (data) {
        AuthController.to.usr.config = ConfigModel.fromJson(data);
        AuthController.to.updateUsr();
      },
      onFailure: (error) {
        ShowToast.fail(error?.msg ?? "未知错误");
      },
    );
  }

  void toCardEditList() async {
    Console.log('to卡片本列表');
    await Get.toNamed(AppRoutes.cardEditList);
    await onRefresh();
    update();
  }

  /// 同步本地数据到云端
  Future<void> syncToCloud() async {
    if (isSyncing) {
      ShowToast.text('同步正在进行中...');
      return;
    }

    try {
      _setSyncStatus('syncing', 0.0);
      ShowToast.loading(val: '正在同步数据...');

      // 检查是否有同步服务可用
      if (!Get.isRegistered<ApiSyncService>()) {
        // 如果没有注册同步服务，先注册
        Get.put<ApiSyncService>(ApiSyncService());
        await Get.find<ApiSyncService>().init();
      }

      final apiSyncService = Get.find<ApiSyncService>();

      // 执行同步
      final success = await apiSyncService.syncAllData();

      if (success) {
        _setSyncStatus('success', 1.0);
        ShowToast.dismiss();
        ShowToast.success('数据同步成功');

        // 同步成功后重新加载书籍列表
        await loadBookList();
      } else {
        _setSyncStatus('failed', 0.0, '同步失败，请检查网络连接');
        ShowToast.dismiss();
        ShowToast.fail('数据同步失败');
      }
    } catch (e) {
      _setSyncStatus('failed', 0.0, e.toString());
      ShowToast.dismiss();
      ShowToast.fail('同步出错: $e');
      Console.log('同步失败: $e');
    }
  }

  /// 仅同步书籍数据
  Future<void> syncBooksOnly() async {
    if (isSyncing) {
      ShowToast.text('同步正在进行中...');
      return;
    }

    try {
      _setSyncStatus('syncing', 0.0);
      ShowToast.loading(val: '正在同步书籍数据...');

      bool success = false;

      // 1. 首先上传本地未同步的书籍
      if (Get.isRegistered<BookDataService>()) {
        final bookDataService = BookDataService.to;
        final uploadSuccess = await bookDataService.syncLocalBooksToApi();
        Console.log('本地书籍上传结果: $uploadSuccess');
        _setSyncStatus('syncing', 0.5);
      }

      // 2. 然后从服务器下载最新的书籍数据
      if (!Get.isRegistered<ApiSyncService>()) {
        Get.put<ApiSyncService>(ApiSyncService());
        await Get.find<ApiSyncService>().init();
      }

      final apiSyncService = Get.find<ApiSyncService>();
      success = await apiSyncService.syncTable('books');

      if (success) {
        _setSyncStatus('success', 1.0);
        ShowToast.dismiss();
        ShowToast.success('书籍数据同步成功');

        // 同步成功后重新加载书籍列表
        await loadBookList();
      } else {
        _setSyncStatus('failed', 0.0, '书籍同步失败');
        ShowToast.dismiss();
        ShowToast.fail('书籍数据同步失败');
      }
    } catch (e) {
      _setSyncStatus('failed', 0.0, e.toString());
      ShowToast.dismiss();
      ShowToast.fail('同步出错: $e');
      Console.log('书籍同步失败: $e');
    }
  }

  /// 设置同步状态
  void _setSyncStatus(String status, double progress, [String? errorMessage]) {
    isSyncing = status == 'syncing';
    syncStatus = status;
    syncProgress = progress;
    syncErrorMessage = errorMessage;
    update();
  }

  /// 重置同步状态
  void resetSyncStatus() {
    _setSyncStatus('idle', 0.0);
  }
}
