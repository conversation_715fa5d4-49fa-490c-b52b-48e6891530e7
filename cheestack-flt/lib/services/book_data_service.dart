part of services;

/// 书籍数据服务
/// 负责管理书籍数据的本地存储和同步
class BookDataService extends GetxService {
  static BookDataService get to => Get.find();
  
  late final DaoManager _daoManager;
  late final UserDataService _userDataService;
  
  Future<BookDataService> init() async {
    // 等待依赖服务初始化完成
    while (!Get.isRegistered<DaoManager>()) {
      await Future.delayed(const Duration(milliseconds: 10));
    }
    while (!Get.isRegistered<UserDataService>()) {
      await Future.delayed(const Duration(milliseconds: 10));
    }

    _daoManager = DaoManager.to;
    _userDataService = UserDataService.to;
    return this;
  }
  
  /// 获取用户的所有书籍
  Future<List<BookModel>> getUserBooks({
    String? orderBy,
    int? limit,
    int? offset,
  }) async {
    final userId = _userDataService.currentUser?.id;
    if (userId == null) return [];
    
    try {
      return await _daoManager.bookDao.findByUserId(
        userId,
        orderBy: orderBy ?? 'created_at DESC',
        limit: limit,
        offset: offset,
      );
    } catch (e) {
      Console.log('Failed to get user books: $e');
      return [];
    }
  }
  
  /// 根据ID获取书籍
  Future<BookModel?> getBookById(int bookId) async {
    try {
      return await _daoManager.bookDao.findById(bookId);
    } catch (e) {
      Console.log('Failed to get book by id: $e');
      return null;
    }
  }
  
  /// 搜索书籍
  Future<List<BookModel>> searchBooks(String keyword, {
    int? limit,
    int? offset,
  }) async {
    final userId = _userDataService.currentUser?.id;
    if (userId == null) return [];
    
    try {
      return await _daoManager.bookDao.searchByName(
        userId,
        keyword,
        limit: limit,
        offset: offset,
      );
    } catch (e) {
      Console.log('Failed to search books: $e');
      return [];
    }
  }
  
  /// 创建书籍
  Future<BookModel?> createBook({
    required String name,
    String? brief,
    String? cover,
    String? privacy,
  }) async {
    final userId = _userDataService.currentUser?.id;
    if (userId == null) return null;
    
    try {
      // 检查书籍名称是否已存在
      final exists = await _daoManager.bookDao.isNameExists(userId, name);
      if (exists) {
        throw Exception('书籍名称已存在');
      }
      
      final book = BookModel(
        name: name,
        brief: brief,
        cover: cover,
        privacy: privacy ?? 'private',
        createdAt: DateTime.now().toIso8601String(),
        updatedAt: DateTime.now().toIso8601String(),
      );
      
      final bookId = await _daoManager.bookDao.insertWithUserId(book, userId);
      
      // 同步到服务器（后台进行）
      _syncBookToApi(book.copyWith(id: bookId));
      
      // 返回创建的书籍
      return book.copyWith(id: bookId);
    } catch (e) {
      Console.log('Failed to create book: $e');
      return null;
    }
  }
  
  /// 更新书籍
  Future<bool> updateBook(int bookId, {
    String? name,
    String? brief,
    String? cover,
    String? privacy,
  }) async {
    try {
      // 检查书籍是否存在
      final existingBook = await _daoManager.bookDao.findById(bookId);
      if (existingBook == null) {
        throw Exception('书籍不存在');
      }
      
      // 如果更新名称，检查是否重复
      if (name != null && name != existingBook.name) {
        final userId = _userDataService.currentUser?.id;
        if (userId != null) {
          final exists = await _daoManager.bookDao.isNameExists(
            userId, 
            name, 
            excludeId: bookId,
          );
          if (exists) {
            throw Exception('书籍名称已存在');
          }
        }
      }
      
      await _daoManager.bookDao.updateBook(
        bookId,
        name: name,
        brief: brief,
        cover: cover,
        privacy: privacy,
      );
      
      // 同步到服务器（后台进行）
      _syncBookUpdateToApi(bookId, {
        if (name != null) 'name': name,
        if (brief != null) 'brief': brief,
        if (cover != null) 'cover': cover,
        if (privacy != null) 'privacy': privacy,
      });
      
      Console.log('Book updated successfully');
      return true;
    } catch (e) {
      Console.log('Failed to update book: $e');
      return false;
    }
  }
  
  /// 删除书籍
  Future<bool> deleteBook(int bookId) async {
    try {
      // 检查书籍是否存在
      final existingBook = await _daoManager.bookDao.findById(bookId);
      if (existingBook == null) {
        throw Exception('书籍不存在');
      }
      
      // 删除书籍下的所有卡片
      await _daoManager.cardDao.deleteAllByBookId(bookId);
      
      // 删除书籍
      await _daoManager.bookDao.delete(bookId);
      
      // 同步到服务器（后台进行）
      _syncBookDeleteToApi(bookId);
      
      Console.log('Book deleted successfully');
      return true;
    } catch (e) {
      Console.log('Failed to delete book: $e');
      return false;
    }
  }
  
  /// 获取书籍统计信息
  Future<Map<String, dynamic>> getBookStats(int bookId) async {
    try {
      final stats = <String, dynamic>{};
      
      // 获取卡片总数
      final totalCards = await _daoManager.cardDao.countByBookId(bookId);
      stats['total_cards'] = totalCards;
      
      // 获取已学习卡片数（有学习记录的卡片）
      final userId = _userDataService.currentUser?.id;
      if (userId != null) {
        // 这里需要根据实际的学习记录表结构来查询
        // 暂时返回0
        stats['studied_cards'] = 0;
        stats['progress'] = totalCards > 0 ? 0.0 : 0.0;
      }
      
      return stats;
    } catch (e) {
      Console.log('Failed to get book stats: $e');
      return {};
    }
  }
  
  /// 获取用户书籍统计
  Future<Map<String, dynamic>> getUserBookStats() async {
    final userId = _userDataService.currentUser?.id;
    if (userId == null) return {};
    
    try {
      final stats = <String, dynamic>{};
      
      // 总书籍数
      stats['total_books'] = await _daoManager.bookDao.countByUserId(userId);
      
      // 公开书籍数
      stats['public_books'] = await _daoManager.bookDao.countPublicByUserId(userId);
      
      // 私有书籍数
      stats['private_books'] = await _daoManager.bookDao.countPrivateByUserId(userId);
      
      // 总卡片数
      stats['total_cards'] = await _daoManager.cardDao.countByUserId(userId);
      
      return stats;
    } catch (e) {
      Console.log('Failed to get user book stats: $e');
      return {};
    }
  }
  
  /// 从API同步书籍数据
  Future<bool> syncBooksFromApi() async {
    final userId = _userDataService.currentUser?.id;
    if (userId == null) return false;
    
    try {
      Console.log('Syncing books from API...');
      
      int skip = 0;
      const int limit = 50;
      bool hasMore = true;
      
      while (hasMore) {
        final response = await OxHttp.to.get(
          '/api/study/v1/books',
          queryParameters: {
            'skip': skip,
            'limit': limit,
            'order': '-created_at',
          },
        );
        
        if (response.statusCode == 200) {
          final List<dynamic> booksData = response.data['data'] ?? [];
          
          if (booksData.isEmpty) {
            hasMore = false;
            break;
          }
          
          for (final bookData in booksData) {
            final book = BookModel(
              id: bookData['id'],
              name: bookData['name'],
              brief: bookData['brief'],
              cover: bookData['cover'],
              privacy: bookData['privacy'],
              createdAt: bookData['created_at'],
              updatedAt: bookData['updated_at'],
            );
            
            // 检查书籍是否已存在
            final existingBook = await _daoManager.bookDao.findById(book.id!);
            if (existingBook != null) {
              await _daoManager.bookDao.update(book);
            } else {
              await _daoManager.bookDao.insertWithUserId(book, userId);
            }
          }
          
          skip += limit;
          if (booksData.length < limit) {
            hasMore = false;
          }
        } else {
          hasMore = false;
        }
      }
      
      Console.log('Books synced from API successfully');
      return true;
    } catch (e) {
      Console.log('Failed to sync books from API: $e');
      return false;
    }
  }
  
  /// 同步书籍到API
  Future<void> _syncBookToApi(BookModel book) async {
    try {
      // 使用FormData支持文件上传
      final formData = FormData.fromMap({
        'name': book.name ?? '',
        'brief': book.brief ?? '',
        'privacy': book.privacy ?? 'free',
      });

      // 如果有封面图片，添加文件
      if (book.cover != null && book.cover!.isNotEmpty) {
        // 检查是否是本地文件路径
        if (book.cover!.startsWith('/') || book.cover!.startsWith('file://')) {
          final file = File(book.cover!.replaceFirst('file://', ''));
          if (await file.exists()) {
            formData.files.add(MapEntry(
              'file',
              await MultipartFile.fromFile(file.path),
            ));
          }
        } else {
          // 如果是URL，直接传递
          formData.fields.add(MapEntry('cover', book.cover!));
        }
      }

      await OxHttp.to.post('/api/study/v1/books', data: formData);
      Console.log('Book synced to API: ${book.name}');
    } catch (e) {
      Console.log('Failed to sync book to API: $e');
    }
  }
  
  /// 同步书籍更新到API
  Future<void> _syncBookUpdateToApi(int bookId, Map<String, dynamic> data) async {
    try {
      await OxHttp.to.put('/api/study/v1/books/$bookId', data: data);
      Console.log('Book update synced to API: $bookId');
    } catch (e) {
      Console.log('Failed to sync book update to API: $e');
    }
  }
  
  /// 同步书籍删除到API
  Future<void> _syncBookDeleteToApi(int bookId) async {
    try {
      await OxHttp.to.delete('/api/study/v1/books/$bookId');
      Console.log('Book deletion synced to API: $bookId');
    } catch (e) {
      Console.log('Failed to sync book deletion to API: $e');
    }
  }

  /// 同步所有未同步的本地书籍到API
  Future<bool> syncLocalBooksToApi() async {
    final userId = _userDataService.currentUser?.id;
    if (userId == null) return false;

    try {
      Console.log('Syncing local books to API...');

      // 获取所有标记为dirty的书籍（未同步的）
      final dirtyBooks = await _daoManager.bookDao.findWhere(
        where: 'user_id = ? AND is_dirty = ?',
        whereArgs: [userId, 1],
      );

      if (dirtyBooks.isEmpty) {
        Console.log('No local books to sync');
        return true;
      }

      Console.log('Found ${dirtyBooks.length} local books to sync');

      int successCount = 0;
      for (final book in dirtyBooks) {
        try {
          await _syncBookToApi(book);

          // 同步成功后，清除dirty标记
          await _daoManager.bookDao.db.update(
            'books',
            {'is_dirty': 0, 'synced_at': DateTime.now().toIso8601String()},
            where: 'id = ?',
            whereArgs: [book.id],
          );

          successCount++;
          Console.log('Book synced successfully: ${book.name}');
        } catch (e) {
          Console.log('Failed to sync book ${book.name}: $e');
        }
      }

      Console.log('Synced $successCount/${dirtyBooks.length} books to API');
      return successCount == dirtyBooks.length;
    } catch (e) {
      Console.log('Failed to sync local books to API: $e');
      return false;
    }
  }
}
