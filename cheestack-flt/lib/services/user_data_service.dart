part of services;

/// 用户数据服务
/// 负责管理用户数据的本地存储和同步
class UserDataService extends GetxService {
  static UserDataService get to => Get.find();

  late final DaoManager _daoManager;

  // 当前用户
  final Rx<UserModel?> _currentUser = Rx<UserModel?>(null);
  final Rx<ConfigModel?> _currentConfig = Rx<ConfigModel?>(null);

  UserModel? get currentUser => _currentUser.value;
  ConfigModel? get currentConfig => _currentConfig.value;

  Future<UserDataService> init() async {
    // 等待依赖服务初始化完成
    while (!Get.isRegistered<DaoManager>()) {
      await Future.delayed(const Duration(milliseconds: 10));
    }

    _daoManager = DaoManager.to;

    // 加载当前用户数据
    await _loadCurrentUser();

    return this;
  }

  /// 加载当前用户数据
  Future<void> _loadCurrentUser() async {
    try {
      final userId = StorageService.to.getString('current_user_id');
      Console.log('UserDataService._loadCurrentUser: 尝试加载用户ID: $userId');

      if (userId.isNotEmpty) {
        // 从本地数据库加载用户数据
        final user = await _daoManager.userDao.findById(userId);
        if (user != null) {
          _currentUser.value = user;

          // 加载用户配置
          final config = await _daoManager.userConfigDao.findByUserId(userId);
          _currentConfig.value = config;

          Console.log('Loaded user from local database: ${user.username}');
        } else {
          Console.log(
              'User not found in local database, trying to sync from API');
          // 本地没有用户数据，尝试从API同步
          await syncCurrentUserFromApi();
        }
      } else {
        Console.log('No current_user_id found in storage');
      }
    } catch (e) {
      Console.log('Failed to load current user: $e');
    }
  }

  /// 重新加载当前用户数据（供外部调用）
  Future<void> reloadCurrentUser() async {
    Console.log('UserDataService.reloadCurrentUser: 开始重新加载用户数据');
    await _loadCurrentUser();
    Console.log(
        'UserDataService.reloadCurrentUser: 重新加载完成，当前用户: ${_currentUser.value?.username}');
  }

  /// 从API同步当前用户数据
  Future<bool> syncCurrentUserFromApi() async {
    try {
      final response = await OxHttp.to.get('/api/auth/me');
      if (response.statusCode == 200) {
        final userData = response.data['data'];

        // 创建用户模型
        final user = UserModel(
          id: userData['id'],
          username: userData['username'],
          mobile: userData['mobile'],
          email: userData['email'],
          avatar: userData['avatar'],
          intro: userData['intro'],
          createdAt: userData['created_at'] != null
              ? DateTime.parse(userData['created_at'])
              : null,
          updatedAt: userData['updated_at'] != null
              ? DateTime.parse(userData['updated_at'])
              : null,
        );

        // 保存到本地数据库
        await _daoManager.userDao.insert(user);
        _currentUser.value = user;

        // 保存用户ID到存储
        await StorageService.to.setString('current_user_id', user.id!);

        // 同步用户配置
        if (userData['config'] != null) {
          await _syncUserConfig(userData['config'], user.id!);
        }

        Console.log('Synced user from API: ${user.username}');
        return true;
      }
    } catch (e) {
      Console.log('Failed to sync user from API: $e');
    }
    return false;
  }

  /// 同步用户配置
  Future<void> _syncUserConfig(
      Map<String, dynamic> configData, String userId) async {
    try {
      final config = ConfigModel(
        isAutoPlayAudio: configData['is_auto_play_audio'] ?? false,
        isAutoPlayAiAudio: configData['is_auto_play_ai_audio'] ?? false,
        reviewNumber: configData['review_number'] ?? 20,
        studyNumber: configData['study_number'] ?? 20,
        studyType: configData['study_type'] ?? 1,
        currentStudyId: configData['current_study_id'],
        editingBookId: configData['editing_book_id'],
        createdAt: configData['created_at'] != null
            ? DateTime.parse(configData['created_at'])
            : null,
        updatedAt: configData['updated_at'] != null
            ? DateTime.parse(configData['updated_at'])
            : null,
      );

      await _daoManager.userConfigDao.insertOrUpdate(userId, config);
      _currentConfig.value = config;

      Console.log('Synced user config');
    } catch (e) {
      Console.log('Failed to sync user config: $e');
    }
  }

  /// 用户登录
  Future<bool> loginUser(String mobile, String verificationCode) async {
    try {
      // 调用登录API
      final response = await OxHttp.to.post('/api/auth/login', data: {
        'mobile': mobile,
        'verification_code': verificationCode,
      });

      if (response.statusCode == 200) {
        final userData = response.data['data'];
        final token = userData['token'];

        // 保存token
        await StorageService.to.setString('token', token);

        // 同步用户数据
        await syncCurrentUserFromApi();

        // 标记为已登录
        await StorageService.to.setBool('isLogin', true);

        return true;
      }
    } catch (e) {
      Console.log('Login failed: $e');
    }
    return false;
  }

  /// 用户登出
  Future<void> logoutUser() async {
    try {
      // 清除本地存储
      await StorageService.to.remove('token');
      await StorageService.to.remove('current_user_id');
      await StorageService.to.setBool('isLogin', false);

      // 清除内存中的用户数据
      _currentUser.value = null;
      _currentConfig.value = null;

      Console.log('User logged out');
    } catch (e) {
      Console.log('Logout failed: $e');
    }
  }

  /// 更新用户资料
  Future<bool> updateUserProfile({
    String? username,
    String? intro,
    String? avatar,
  }) async {
    if (_currentUser.value == null) return false;

    try {
      final userId = _currentUser.value!.id!;

      // 更新本地数据库
      await _daoManager.userDao.updateProfile(
        userId,
        username: username,
        intro: intro,
        avatar: avatar,
      );

      // 更新内存中的用户数据
      final updatedUser = _currentUser.value!.copyWith(
        username: username ?? _currentUser.value!.username,
        intro: intro ?? _currentUser.value!.intro,
        avatar: avatar ?? _currentUser.value!.avatar,
        updatedAt: DateTime.now(),
      );
      _currentUser.value = updatedUser;

      // 同步到服务器（后台进行）
      _syncUserProfileToApi(username: username, intro: intro, avatar: avatar);

      Console.log('User profile updated');
      return true;
    } catch (e) {
      Console.log('Failed to update user profile: $e');
      return false;
    }
  }

  /// 同步用户资料到API
  Future<void> _syncUserProfileToApi({
    String? username,
    String? intro,
    String? avatar,
  }) async {
    try {
      final data = <String, dynamic>{};
      if (username != null) data['username'] = username;
      if (intro != null) data['intro'] = intro;
      if (avatar != null) data['avatar'] = avatar;

      if (data.isNotEmpty) {
        await OxHttp.to.put('/api/auth/me', data: data);
        Console.log('User profile synced to API');
      }
    } catch (e) {
      Console.log('Failed to sync user profile to API: $e');
    }
  }

  /// 更新用户配置
  Future<bool> updateUserConfig({
    bool? isAutoPlayAudio,
    bool? isAutoPlayAiAudio,
    int? reviewNumber,
    int? studyNumber,
    int? studyType,
    int? currentStudyId,
    int? editingBookId,
  }) async {
    if (_currentUser.value == null) return false;

    try {
      final userId = _currentUser.value!.id!;

      // 更新本地数据库
      await _daoManager.userConfigDao.updateStudyConfig(
        userId,
        reviewNumber: reviewNumber,
        studyNumber: studyNumber,
        studyType: studyType,
      );

      await _daoManager.userConfigDao.updateAudioConfig(
        userId,
        isAutoPlayAudio: isAutoPlayAudio,
        isAutoPlayAiAudio: isAutoPlayAiAudio,
      );

      if (currentStudyId != null) {
        await _daoManager.userConfigDao
            .updateCurrentStudyId(userId, currentStudyId);
      }

      if (editingBookId != null) {
        await _daoManager.userConfigDao
            .updateEditingBook(userId, editingBookId);
      }

      // 更新内存中的配置数据
      final currentConfig = _currentConfig.value ?? ConfigModel();
      _currentConfig.value = currentConfig.copyWith(
        isAutoPlayAudio: isAutoPlayAudio ?? currentConfig.isAutoPlayAudio,
        isAutoPlayAiAudio: isAutoPlayAiAudio ?? currentConfig.isAutoPlayAiAudio,
        reviewNumber: reviewNumber ?? currentConfig.reviewNumber,
        studyNumber: studyNumber ?? currentConfig.studyNumber,
        studyType: studyType ?? currentConfig.studyType,
        currentStudyId: currentStudyId ?? currentConfig.currentStudyId,
        editingBookId: editingBookId ?? currentConfig.editingBookId,
        updatedAt: DateTime.now(),
      );

      // 同步到服务器（后台进行）
      _syncUserConfigToApi();

      Console.log('User config updated');
      return true;
    } catch (e) {
      Console.log('Failed to update user config: $e');
      return false;
    }
  }

  /// 同步用户配置到API
  Future<void> _syncUserConfigToApi() async {
    try {
      if (_currentConfig.value != null) {
        final config = _currentConfig.value!;
        final data = {
          'is_auto_play_audio': config.isAutoPlayAudio,
          'is_auto_play_ai_audio': config.isAutoPlayAiAudio,
          'review_number': config.reviewNumber,
          'study_number': config.studyNumber,
          'study_type': config.studyType,
          'current_study_id': config.currentStudyId,
          'editing_book_id': config.editingBookId,
        };

        await OxHttp.to.put('/api/auth/configs/me', data: data);
        Console.log('User config synced to API');
      }
    } catch (e) {
      Console.log('Failed to sync user config to API: $e');
    }
  }

  /// 获取用户统计数据
  Future<Map<String, dynamic>> getUserStats() async {
    if (_currentUser.value == null) return {};

    try {
      final userId = _currentUser.value!.id!;
      return await _daoManager.getUserDataStats(userId);
    } catch (e) {
      Console.log('Failed to get user stats: $e');
      return {};
    }
  }

  /// 检查是否已登录
  bool isLoggedIn() {
    return _currentUser.value != null &&
        StorageService.to.getBool('isLogin') == true;
  }
}
