import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:cheestack_flt/services/index.dart';
import 'package:cheestack_flt/services/dao/dao_manager.dart';
import 'package:cheestack_flt/models/index.dart';
import 'package:cheestack_flt/global.dart';

void main() {
  group('BookDataService Tests', () {
    setUpAll(() async {
      // 初始化全局服务
      await Global().init();
    });

    tearDownAll(() {
      // 清理
      Get.reset();
    });

    test('should create book successfully', () async {
      // 等待服务初始化完成
      await Future.delayed(const Duration(seconds: 2));

      // 检查服务是否注册
      expect(Get.isRegistered<BookDataService>(), true);
      expect(Get.isRegistered<UserDataService>(), true);
      expect(Get.isRegistered<DaoManager>(), true);

      final bookDataService = BookDataService.to;
      final userDataService = UserDataService.to;

      // 检查用户是否已登录
      print('当前用户: ${userDataService.currentUser?.username}');
      print('用户ID: ${userDataService.currentUser?.id}');

      if (userDataService.currentUser == null) {
        // 如果没有用户，创建一个测试用户
        print('创建测试用户...');
        // 这里需要根据实际情况创建用户
        return;
      }

      // 测试创建书籍
      final result = await bookDataService.createBook(
        name: '测试书籍_${DateTime.now().millisecondsSinceEpoch}',
        brief: '这是一个测试书籍',
        privacy: 'private',
      );

      print('创建结果: $result');
      expect(result, isNotNull);
      expect(result!.name, contains('测试书籍'));
    });

    test('should handle duplicate book name', () async {
      await Future.delayed(const Duration(seconds: 1));

      final bookDataService = BookDataService.to;

      // 创建第一个书籍
      final bookName = '重复名称测试_${DateTime.now().millisecondsSinceEpoch}';
      final result1 = await bookDataService.createBook(
        name: bookName,
        brief: '第一个书籍',
        privacy: 'private',
      );

      expect(result1, isNotNull);

      // 尝试创建同名书籍
      final result2 = await bookDataService.createBook(
        name: bookName,
        brief: '第二个书籍',
        privacy: 'private',
      );

      // 应该返回null，因为名称重复
      expect(result2, isNull);
    });
  });
}
